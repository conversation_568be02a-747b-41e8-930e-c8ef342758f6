#!/bin/bash

# Test script for the new public domains endpoint
# This script tests various scenarios for the GET /public/domains endpoint

BASE_URL="http://localhost:8080/api/v1"
API_KEY="your-api-key-here"

echo "Testing Public Domains API Endpoint"
echo "===================================="

# Test 1: Missing API key
echo "Test 1: Missing API key (should return 401)"
curl -s -w "\nHTTP Status: %{http_code}\n" \
  "${BASE_URL}/public/domains?line_code=test&name=test"
echo ""

# Test 2: Invalid API key
echo "Test 2: Invalid API key (should return 401)"
curl -s -w "\nHTTP Status: %{http_code}\n" \
  -H "X-API-KEY: invalid-key" \
  "${BASE_URL}/public/domains?line_code=test&name=test"
echo ""

# Test 3: Missing required parameters
echo "Test 3: Missing required parameters (should return 400)"
curl -s -w "\nHTTP Status: %{http_code}\n" \
  -H "X-API-KEY: ${API_KEY}" \
  "${BASE_URL}/public/domains"
echo ""

# Test 4: Missing line_code parameter
echo "Test 4: Missing line_code parameter (should return 400)"
curl -s -w "\nHTTP Status: %{http_code}\n" \
  -H "X-API-KEY: ${API_KEY}" \
  "${BASE_URL}/public/domains?name=test"
echo ""

# Test 5: Missing name parameter
echo "Test 5: Missing name parameter (should return 400)"
curl -s -w "\nHTTP Status: %{http_code}\n" \
  -H "X-API-KEY: ${API_KEY}" \
  "${BASE_URL}/public/domains?line_code=test"
echo ""

# Test 6: Valid request with default pagination
echo "Test 6: Valid request with default pagination"
curl -s -w "\nHTTP Status: %{http_code}\n" \
  -H "X-API-KEY: ${API_KEY}" \
  "${BASE_URL}/public/domains?line_code=test&name=test" | jq '.'
echo ""

# Test 7: Valid request with custom pagination
echo "Test 7: Valid request with custom pagination"
curl -s -w "\nHTTP Status: %{http_code}\n" \
  -H "X-API-KEY: ${API_KEY}" \
  "${BASE_URL}/public/domains?line_code=test&name=test&page=1&page_size=5" | jq '.'
echo ""

# Test 8: Valid request with large page size (should be limited to 100)
echo "Test 8: Valid request with large page size"
curl -s -w "\nHTTP Status: %{http_code}\n" \
  -H "X-API-KEY: ${API_KEY}" \
  "${BASE_URL}/public/domains?line_code=test&name=test&page=1&page_size=200" | jq '.'
echo ""

echo "Testing completed!"
