#!/bin/bash

# Updated test script for the public domains endpoint
# Tests the new optional parameter behavior

BASE_URL="http://localhost:8080/api/v1"
API_KEY="your-api-key-here"

echo "Testing Public Domains API Endpoint (Updated)"
echo "=============================================="

# Test 1: Missing API key
echo "Test 1: Missing API key (should return 401)"
curl -s -w "\nHTTP Status: %{http_code}\n" \
  "${BASE_URL}/public/domains?line_code=test&name=test"
echo ""

# Test 2: Invalid API key
echo "Test 2: Invalid API key (should return 401)"
curl -s -w "\nHTTP Status: %{http_code}\n" \
  -H "X-API-KEY: invalid-key" \
  "${BASE_URL}/public/domains?line_code=test&name=test"
echo ""

# Test 3: No parameters (should return all domains)
echo "Test 3: No parameters (should return all domains)"
curl -s -w "\nHTTP Status: %{http_code}\n" \
  -H "X-API-KEY: ${API_KEY}" \
  "${BASE_URL}/public/domains" | jq '.'
echo ""

# Test 4: Only line_code parameter (should work)
echo "Test 4: Only line_code parameter (should work)"
curl -s -w "\nHTTP Status: %{http_code}\n" \
  -H "X-API-KEY: ${API_KEY}" \
  "${BASE_URL}/public/domains?line_code=bk10" | jq '.'
echo ""

# Test 5: Only name parameter (should work)
echo "Test 5: Only name parameter (should work)"
curl -s -w "\nHTTP Status: %{http_code}\n" \
  -H "X-API-KEY: ${API_KEY}" \
  "${BASE_URL}/public/domains?name=test" | jq '.'
echo ""

# Test 6: Both parameters (should work)
echo "Test 6: Both parameters (should work)"
curl -s -w "\nHTTP Status: %{http_code}\n" \
  -H "X-API-KEY: ${API_KEY}" \
  "${BASE_URL}/public/domains?line_code=bk10&name=test" | jq '.'
echo ""

# Test 7: With pagination - no filters
echo "Test 7: With pagination - no filters"
curl -s -w "\nHTTP Status: %{http_code}\n" \
  -H "X-API-KEY: ${API_KEY}" \
  "${BASE_URL}/public/domains?page=1&page_size=5" | jq '.'
echo ""

# Test 8: With pagination and line_code filter
echo "Test 8: With pagination and line_code filter"
curl -s -w "\nHTTP Status: %{http_code}\n" \
  -H "X-API-KEY: ${API_KEY}" \
  "${BASE_URL}/public/domains?line_code=bk10&page=1&page_size=5" | jq '.'
echo ""

# Test 9: With pagination and name filter
echo "Test 9: With pagination and name filter"
curl -s -w "\nHTTP Status: %{http_code}\n" \
  -H "X-API-KEY: ${API_KEY}" \
  "${BASE_URL}/public/domains?name=test&page=1&page_size=5" | jq '.'
echo ""

# Test 10: Large page size (should be limited to 100)
echo "Test 10: Large page size (should be limited to 100)"
curl -s -w "\nHTTP Status: %{http_code}\n" \
  -H "X-API-KEY: ${API_KEY}" \
  "${BASE_URL}/public/domains?page=1&page_size=200" | jq '.'
echo ""

echo "Testing completed!"
