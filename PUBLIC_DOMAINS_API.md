# Public Domains API Endpoint

## Overview
This document describes the new public API endpoint for retrieving domains filtered by order information.

## Endpoint Details

### GET /api/v1/public/domains

Retrieves domains filtered by order line_code and order name with pagination support.

#### Authentication
- **Type**: API Key
- **Header**: `X-API-KEY`
- **Required**: Yes

#### Query Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `line_code` | string | Yes | - | Filter domains by order line_code |
| `name` | string | Yes | - | Filter domains by order name |
| `page` | integer | No | 1 | Page number for pagination (minimum: 1) |
| `page_size` | integer | No | 10 | Number of items per page (minimum: 1, maximum: 100) |

#### Response Format

```json
{
  "status": true,
  "message": "Domains retrieved successfully",
  "data": {
    "content": [
      {
        "id": 1,
        "name": "example.com",
        "is_default": true,
        "is_active": true,
        "zone_id": "zone123",
        "account_id": "acc123",
        "account_name": "Example Account",
        "namespace_id": 1,
        "index": 1,
        "created_at": "2025-01-01T00:00:00Z",
        "update_at": "2025-01-01T00:00:00Z",
        "namespace": {
          "id": 1,
          "name": "example-namespace",
          "slug": "example-namespace",
          "is_active": true,
          "type": "project",
          "cluster_id": 1
        }
      }
    ],
    "pageable": {
      "pageNumber": 1,
      "pageSize": 10,
      "sort": {
        "empty": false,
        "unsorted": true,
        "sorted": false
      },
      "offset": 0,
      "paged": true,
      "unpaged": false
    },
    "last": true,
    "totalElements": 1,
    "totalPages": 1,
    "first": true,
    "size": 10,
    "number": 1,
    "sort": {
      "empty": true,
      "unsorted": true,
      "sorted": false
    },
    "numberOfElements": 1,
    "empty": false
  }
}
```

#### Error Responses

##### 401 Unauthorized - Missing API Key
```json
{
  "error": "Unauthorized",
  "message": "X-API-KEY header required",
  "code": 401
}
```

##### 401 Unauthorized - Invalid API Key
```json
{
  "error": "Unauthorized",
  "message": "Invalid API key",
  "code": 401
}
```

##### 400 Bad Request - Missing Required Parameters
```json
{
  "status": false,
  "message": "Both line_code and name query parameters are required"
}
```

##### 500 Internal Server Error
```json
{
  "status": false,
  "message": "Internal server error message"
}
```

## Usage Examples

### Basic Request
```bash
curl -H "X-API-KEY: your-api-key-here" \
  "http://localhost:8080/api/v1/public/domains?line_code=ABC123&name=example"
```

### Request with Pagination
```bash
curl -H "X-API-KEY: your-api-key-here" \
  "http://localhost:8080/api/v1/public/domains?line_code=ABC123&name=example&page=2&page_size=20"
```

### Request with Partial Matching
```bash
curl -H "X-API-KEY: your-api-key-here" \
  "http://localhost:8080/api/v1/public/domains?line_code=ABC&name=exam"
```

## Configuration

### Environment Variables
Add the following environment variable to your `.env` file:

```env
API_KEY=your-secure-api-key-here
```

### Security Considerations
- The API key should be kept secure and not exposed in client-side code
- Use HTTPS in production environments
- Consider implementing rate limiting for production use
- Rotate API keys regularly

## Implementation Details

### Database Queries
The endpoint performs joins between the following tables:
- `domains` - Main domain records
- `order_namespace` - Links orders to namespaces
- `order` - Order information containing line_code and name

### Filtering Logic
- `line_code`: Uses LIKE matching with wildcards (`%value%`)
- `name`: Uses LIKE matching with wildcards (`%value%`)
- Results are deduplicated using `DISTINCT` to handle multiple order relationships

### Pagination
- Default page size: 10
- Maximum page size: 100
- Page numbers start from 1
- Total count is calculated before applying pagination limits
