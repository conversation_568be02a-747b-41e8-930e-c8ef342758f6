package repository

import (
	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"

	"gorm.io/gorm"
)

type DomainRepository struct {
	db *gorm.DB
}

func NewDomainRepository(db *gorm.DB) ports.DomainRepository {
	return &DomainRepository{db: db}
}

func (r *DomainRepository) Insert(domain *domain.Domain) error {
	err := r.db.Create(domain).Error
	if err != nil {
		return err
	}

	// Reload the dns with its relationships
	return r.db.Preload("Namespace").First(domain, domain.ID).Error
}

func (r *DomainRepository) FindByID(id uint64) (*domain.Domain, error) {
	var domain domain.Domain
	err := r.db.Preload("Namespace").
		Preload("Namespace.Cluster").
		First(&domain, id).Error
	if err != nil {
		return nil, err
	}
	return &domain, nil
}

func (r *DomainRepository) FindAll(filter *ports.DomainFilter) ([]*domain.Domain, error) {
	var domains []*domain.Domain
	query := r.db.Preload("Namespace").
		Preload("Namespace.Cluster").
		Order("index DESC")

	if filter != nil {
		if filter.Name != nil {
			query = query.Where("name LIKE ?", "%"+*filter.Name+"%")
		}
		if filter.IsDefault != nil {
			query = query.Where("is_default = ?", *filter.IsDefault)
		}
		if filter.IsActive != nil {
			query = query.Where("is_active = ?", *filter.IsActive)
		}
		if filter.NamespaceID != nil {
			query = query.Where("namespace_id = ?", *filter.NamespaceID)
		}
	}

	err := query.Find(&domains).Error
	if err != nil {
		return nil, err
	}
	return domains, nil
}

func (r *DomainRepository) Update(domain *domain.Domain) error {
	return r.db.Save(domain).Error
}

func (r *DomainRepository) Delete(id uint64) error {
	return r.db.Delete(&domain.Domain{}, id).Error
}

func (r *DomainRepository) DeleteByNamespaceID(namespaceID uint64) error {
	return r.db.Where("namespace_id = ?", namespaceID).Delete(&domain.Domain{}).Error
}

func (r *DomainRepository) FindAllWithPagination(filter *ports.DomainFilter) ([]*domain.Domain, uint64, error) {
	var domains []*domain.Domain
	var total int64

	// Build base query with preloads
	query := r.db.Preload("Namespace").
		Preload("Namespace.Cluster").
		Order("index DESC")

	// Apply basic domain filters
	if filter != nil {
		if filter.Name != nil {
			query = query.Where("name LIKE ?", "%"+*filter.Name+"%")
		}
		if filter.IsDefault != nil {
			query = query.Where("is_default = ?", *filter.IsDefault)
		}
		if filter.IsActive != nil {
			query = query.Where("is_active = ?", *filter.IsActive)
		}
		if filter.NamespaceID != nil {
			query = query.Where("namespace_id = ?", *filter.NamespaceID)
		}

		// Apply order-related filters using joins
		if filter.OrderLineCode != nil || filter.OrderName != nil {
			// Join with order_namespace to get order relationships
			query = query.Table("domains").
				Joins("JOIN order_namespace ON domains.namespace_id = order_namespace.namespace_id").
				Joins("JOIN \"order\" ON order_namespace.order_id = \"order\".id")

			if filter.OrderLineCode != nil {
				query = query.Where("\"order\".line_code LIKE ?", "%"+*filter.OrderLineCode+"%")
			}
			if filter.OrderName != nil {
				query = query.Where("\"order\".name LIKE ?", "%"+*filter.OrderName+"%")
			}

			// Use DISTINCT to avoid duplicate domains from multiple order relationships
			query = query.Distinct("domains.id")
		}
	}

	// Get total count before applying pagination
	countQuery := query.Session(&gorm.Session{})
	if err := countQuery.Model(&domain.Domain{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination
	if filter != nil && filter.Page != nil && filter.PageSize != nil {
		offset := (*filter.Page - 1) * *filter.PageSize
		query = query.Offset(int(offset)).Limit(int(*filter.PageSize))
	}

	err := query.Find(&domains).Error
	if err != nil {
		return nil, 0, err
	}

	return domains, uint64(total), nil
}
