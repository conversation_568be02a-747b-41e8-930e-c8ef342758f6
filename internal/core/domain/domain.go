package domain

type Domain struct {
	BaseModel
	Name        string `json:"name" gorm:"not null"`
	IsDefault   bool   `json:"is_default"`
	IsActive    bool   `json:"is_active"`
	ZoneID      string `json:"zone_id" gorm:"not null"`
	AccountID   string `json:"account_id" gorm:"not null"`
	AccountName string `json:"account_name" gorm:"not null"`
	NamespaceID uint64 `json:"namespace_id" gorm:"not null"`
	Index       int    `json:"index"`

	// Order-related fields (populated when querying with order filters)
	OrderLineCode *string `json:"order_line_code,omitempty" gorm:"-"`
	OrderName     *string `json:"order_name,omitempty" gorm:"-"`

	// Relationships
	Namespace *Namespace `json:"namespace,omitempty" gorm:"foreignKey:NamespaceID"`
}
